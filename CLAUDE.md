# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This repository contains planning documents and a demo implementation for a guitar tutoring application with video analysis capabilities. The project is divided into two main components:

1. **Guitar Tutor Build Plans**: Three comprehensive build plans for different approaches to guitar coaching apps
2. **Basketball Demo**: A working computer vision demo showing AI-powered sports analysis

## Architecture

### Core Project Structure

- **guitar-tutor-build-plan.rst**: Real-time guitar coaching app using React Native + MediaPipe
- **guitar-tutor-video-analysis-build-plan.rst**: Asynchronous video analysis app with Rails backend + Gemini AI
- **mlp-video-analysis-app-plan.rst**: Minimum Lovable Product (MLP) version using serverless architecture
- **gemini-bball/**: Working demo of video analysis with AI feedback

### Guitar Tutor Technical Stack

The plans outline three different technical approaches:

1. **Real-time approach**: React Native + MediaPipe Hand Landmarker + SQLite
2. **Asynchronous approach**: React Native client + Rails API + Gemini 1.5 Pro + PostgreSQL
3. **MLP approach**: React Native + Serverless functions + Gemini 1.5 Pro

### Basketball Demo (gemini-bball/)

**Key Components:**
- `ball.py`: OpenCV + MediaPipe pose detection with shot tracking overlay
- `ball.json`: AI-generated shot analysis data from Gemini
- `final_ball.mov`: Source video file

**Processing Pipeline:**
1. MediaPipe tracks player head position for arrow overlay
2. Pre-analyzed shot data (timestamps, results, feedback) displays in real-time
3. Color-coded animations show shot outcomes (green=made, red=missed)
4. AI coaching feedback appears for 4 seconds after each shot

## Common Development Commands

### Basketball Demo
```bash
# Run the basketball analysis demo
cd gemini-bball
python ball.py
```

**Dependencies for basketball demo:**
- OpenCV (`pip install opencv-python`)
- MediaPipe (`pip install mediapipe`)
- NumPy (`pip install numpy`)

### File Path Configuration

The basketball demo has hardcoded paths in `ball.py:21-22`:
- Processing video: `/Users/<USER>/Developer/tidbit-script/final_ball.mov`
- Display video: `final_ball.mp4`

Update these paths to match your local file structure.

## Key Implementation Notes

### Guitar Tutor Calibration Challenge

The most critical technical challenge across all guitar tutor approaches is the **Calibration Engine** - mapping camera 2D space to guitar fretboard coordinates. This requires:

1. Fretboard edge detection
2. Fret and string line identification  
3. Coordinate system transformation from pixels to (fret, string) positions

### AI Integration Patterns

The basketball demo demonstrates the AI analysis pattern used in the guitar tutor plans:

1. **Pre-analysis**: Video is analyzed by Gemini AI to generate structured JSON
2. **Real-time overlay**: JSON data drives real-time visual feedback during playback
3. **Synchronized feedback**: Timestamps link AI insights to specific video moments

### Video Processing Performance

The basketball demo shows performance optimization techniques:
- Process frames at reduced FPS (3 FPS) for pose detection
- Display at full resolution for smooth playback
- Cache processed frames for final video output

## Business Model Context

The guitar tutor plans include a two-app monetization strategy:
1. **Freemium real-time app**: User acquisition and primary revenue
2. **Premium analysis app**: High-value subscription service with video analysis credits

This repository serves as the foundation for implementing either approach, with the basketball demo providing a working reference for the video analysis pipeline.