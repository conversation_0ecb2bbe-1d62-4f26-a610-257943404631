# AI Coder Prompt: Guitar Coach Proof of Concept Implementation

## Overview
I need you to implement a working guitar coaching demo that analyzes a practice video and overlays real-time feedback, similar to a basketball shot analysis demo. This is a proof of concept to validate the technical approach before building a full mobile app.

## What I Need You to Build
A Python script that:
1. Loads my guitar practice video
2. Uses MediaPipe to track hand landmarks
3. Overlays coaching feedback at specific timestamps
4. Displays chord names, accuracy scores, and coaching tips
5. Outputs a final analyzed video

## Technical Requirements

### Dependencies
```bash
pip install opencv-python mediapipe numpy
```

### Input Files I Will Provide
1. **guitar_practice.mp4** - My guitar practice video
2. **guitar_analysis.json** - AI analysis results (format below)

### Expected Output
- **guitar_analysis_final.mp4** - Video with coaching overlays
- Real-time display showing analysis as it processes
- Console output with session summary

## JSON Data Structure
You'll work with this JSON format from my Gemini analysis:

```json
{
  "chord_attempts": [
    {
      "timestamp": "0:05.2",
      "chord_attempted": "G Major",
      "visual_accuracy": 85,
      "audio_quality": 70,
      "finger_positions": {
        "finger_1": {"fret": 2, "string": 5, "correct": true},
        "finger_2": {"fret": 3, "string": 6, "correct": false},
        "finger_3": {"fret": 3, "string": 1, "correct": true}
      },
      "issues_detected": ["finger 2 positioned too high", "string 6 slightly muted"],
      "coaching_tip": "Move your middle finger down closer to the fret wire for cleaner sound",
      "overall_score": 78
    }
  ],
  "session_summary": {
    "total_attempts": 4,
    "average_accuracy": 75,
    "most_common_issue": "finger positioning too high on frets",
    "improvement_suggestion": "Focus on finger placement closer to fret wires"
  }
}
```

## MY ACTUAL JSON OUTPUT FROM GEMINI

```json
{
  "chord_attempts": [
    {
      "timestamp": "0:57.0",
      "chord_attempted": "G Major",
      "visual_accuracy": 95,
      "audio_quality": 90,
      "finger_positions": {
        "finger_1": {"fret": 2, "string": 5, "correct": true},
        "finger_2": {"fret": 3, "string": 6, "correct": true},
        "finger_3": {"fret": 3, "string": 1, "correct": true}
      },
      "issues_detected": [],
      "coaching_tip": "Great job on the G major! The fingering iss clean and the sound is clear.",
      "overall_score": 93
    },
    {
      "timestamp": "0:59.7",
      "chord_attempted": "C Major",
      "visual_accuracy": 90,
      "audio_quality": 95,
      "finger_positions": {
        "finger_1": {"fret": 1, "string": 2, "correct": true},
        "finger_2"s: {"fret": 2, "string": 4, "correct": true},
        "finger_3": {"fret": 3, "string": 5, "correct": true}
      },
      "issues_detected": [],
      "coaching_tip": "Excellent C major! Very clear and well-fingered.",
      "overall_score": 93
    },
    {
      "timestamp": "1:01.1",
      "chord_attempted": "D Major",
      "visual_accuracy": 85,
      "audio_quality": 80,
      "finger_positions": {
        "finger_1": {"fret": 2, "string": 3, "correct": true},
        "finger_2": {"fret": 3, "string": 1, "correct": true},
        "finger_3": {"fret": 2, "string": 2, "correct": true}
      },
      "issues_detected": ["finger 3 slightly impedes string 1"],
      "coaching_tip": "You're very close on the D major! Try to arch your third finger slightly more so it doesn't mute the B string.",
      "overall_score": 83
    },
    {
      "timestamp": "1:02.7",
      "chord_attempted": "E Minor",
      "visual_accuracy": 95,
      "audio_quality": 98,
      "finger_positions": {
        "finger_1": {"fret": 2, "string": 5, "correct": true},
        "finger_2": {"fret": 2, "string": 4, "correct": true}
      },
      "issues_detected": [],
      "coaching_tip": "Perfect E minor! That's a great sounding chord.",
      "overall_score": 97
    }
  ],
  "session_summary": {
    "total_attempts": 4,
    "average_accuracy": 91.5,
    "most_common_issue": "finger placement in D major",
    "improvement_suggestion": "Continue practicing clean finger placement, ensuring fingers are arched to avoid muting adjacent strings."
  }
}
```

## Visual Overlay Requirements

### Top Left Corner Display
- Current chord being attempted (e.g., "Chord: G Major")
- Accuracy score with color coding (e.g., "Accuracy: 78%")
- Text with black border for readability

### Color Coding System
- **Green**: Good performance (80%+ accuracy)
- **Yellow**: Okay performance (60-79% accuracy)  
- **Red**: Needs improvement (<60% accuracy)

### Bottom Section Feedback
- Coaching tips appearing at specific timestamps
- 4-second display duration for each tip
- Centered text, wrapped for readability
- White text with black border

### Hand Tracking
- Use MediaPipe to draw hand landmarks on both hands
- Show connection lines between joints
- Real-time hand position tracking

## Technical Implementation Details

### Video Processing
- Process frame by frame
- Convert timestamps to frame numbers
- Handle video at original resolution
- Maintain smooth playback

### MediaPipe Integration
```python
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(
    static_image_mode=False,
    max_num_hands=2,
    min_detection_confidence=0.5,
    min_tracking_confidence=0.5
)
```

### Feedback Timing
- Parse timestamps like "0:05.2" to frame numbers
- Display feedback for 4 seconds after each chord attempt
- Smooth color transitions based on accuracy scores

### Text Rendering
- Use cv2.putText with custom styling
- Implement text wrapping for long coaching tips
- Add black borders for text readability
- Scale text appropriately for video resolution

## Reference Implementation
Base this on the basketball demo approach in `ball.py` but adapt for guitar:

**Key Differences from Basketball Demo:**
- Track hands instead of head position
- Display chord info instead of shot statistics
- Show coaching feedback instead of shot results
- Use guitar-specific color coding and feedback

## File Structure Expected
```
project_folder/
├── guitar_practice.mp4        # My video file
├── guitar_analysis.json       # My JSON analysis
├── guitar_coach.py           # Your implementation
└── guitar_analysis_final.mp4 # Generated output
```

## Success Criteria
1. **Video loads and processes without errors**
2. **Hand landmarks are tracked and displayed**
3. **Chord names and accuracy scores appear in top left**
4. **Coaching feedback appears at correct timestamps**
5. **Color coding works based on accuracy scores**
6. **Final video is saved with all overlays**
7. **Processing is smooth and efficient**

## Error Handling
- Graceful handling if video file not found
- Fallback if JSON parsing fails
- Continue processing if hand tracking fails temporarily
- Inform user of any issues during processing

## Performance Considerations
- Process at reasonable frame rate
- Don't skip important feedback moments
- Maintain video quality in output
- Optimize for smooth real-time display

## Expected User Experience
1. Run `python guitar_coach.py`
2. See real-time analysis window with overlays
3. Watch coaching feedback appear at timestamps
4. Get final analyzed video saved automatically
5. See session summary in console

## Additional Features (Nice to Have)
- Progress bar showing processing status
- Ability to pause/resume during playback
- Option to adjust feedback display duration
- Different color schemes for accessibility

## Testing
After implementation, the script should:
- Work with my specific video and JSON files
- Display meaningful coaching feedback
- Create a professional-looking analyzed video
- Run smoothly without crashes
- Provide clear visual feedback about guitar performance

Please implement this guitar coaching demo and ensure it works with my specific JSON data structure and video file. The goal is to create a compelling proof of concept that demonstrates the value of AI-powered guitar coaching.

## Questions for Clarification
If you need any clarification about:
- Video processing requirements
- JSON data structure
- Visual overlay specifications
- Performance requirements
- Error handling approach

Please ask before starting implementation.
