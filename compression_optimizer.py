#!/usr/bin/env python3
"""
Guitar Coaching Video Compression Optimizer

Analyzes and optimizes FFmpeg settings to reduce file size while maintaining quality
and minimizing performance impact.

Original issue: 23MB → 113MB (4.9x increase)
Target: Reduce to <40MB while keeping processing time <6 seconds
"""

import json
import subprocess
import os
import time
import sys
from pathlib import Path

class CompressionOptimizer:
    def __init__(self):
        self.test_video = 'guitar_practice.mp4'
        self.test_output_dir = 'compression_tests'
        self.results = []
        
        # Ensure test directory exists
        os.makedirs(self.test_output_dir, exist_ok=True)
    
    def get_video_info(self, video_path):
        """Get detailed video information"""
        cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', 
               '-show_format', '-show_streams', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            data = json.loads(result.stdout)
            video_stream = next(s for s in data['streams'] if s['codec_type'] == 'video')
            
            return {
                'size_mb': float(data['format']['size']) / (1024 * 1024),
                'duration': float(data['format']['duration']),
                'video_bitrate': int(video_stream.get('bit_rate', 0)),
                'width': video_stream['width'],
                'height': video_stream['height'],
                'total_bitrate': int(data['format']['bit_rate'])
            }
        return None
    
    def test_compression_settings(self, name, ffmpeg_args, description):
        """Test specific FFmpeg compression settings"""
        print(f"\n🧪 Testing {name}: {description}")
        
        output_path = os.path.join(self.test_output_dir, f'test_{name.lower().replace(" ", "_")}.mp4')
        
        # Build FFmpeg command
        cmd = ['ffmpeg', '-y', '-i', self.test_video] + ffmpeg_args + [output_path]
        
        # Measure processing time
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        processing_time = time.time() - start_time
        
        if result.returncode == 0:
            # Get output file info
            info = self.get_video_info(output_path)
            if info:
                original_info = self.get_video_info(self.test_video)
                
                result_data = {
                    'name': name,
                    'description': description,
                    'processing_time': processing_time,
                    'file_size_mb': info['size_mb'],
                    'compression_ratio': original_info['size_mb'] / info['size_mb'],
                    'size_reduction_percent': (1 - info['size_mb'] / original_info['size_mb']) * 100,
                    'bitrate_kbps': info['total_bitrate'] // 1000,
                    'ffmpeg_args': ffmpeg_args,
                    'success': True
                }
                
                self.results.append(result_data)
                
                print(f"   ✅ Success: {info['size_mb']:.1f}MB ({result_data['size_reduction_percent']:.1f}% reduction)")
                print(f"   ⏱️  Time: {processing_time:.1f}s")
                print(f"   📊 Bitrate: {info['total_bitrate']//1000}kbps")
                
                return result_data
        
        print(f"   ❌ Failed: {result.stderr}")
        return None
    
    def run_comprehensive_tests(self):
        """Run comprehensive compression tests"""
        print("🎸 Guitar Coaching Video Compression Optimization")
        print("=" * 60)
        
        # Get original video info
        original_info = self.get_video_info(self.test_video)
        print(f"📹 Original Video: {original_info['size_mb']:.1f}MB, {original_info['total_bitrate']//1000}kbps")
        
        # Test configurations
        test_configs = [
            {
                'name': 'Current Settings',
                'args': ['-c:v', 'libx264', '-preset', 'ultrafast', '-crf', '23', '-c:a', 'copy'],
                'description': 'Current ultrafast preset with CRF 23'
            },
            {
                'name': 'Balanced Fast',
                'args': ['-c:v', 'libx264', '-preset', 'fast', '-crf', '26', '-c:a', 'copy'],
                'description': 'Fast preset with higher CRF for better compression'
            },
            {
                'name': 'Optimized Medium',
                'args': ['-c:v', 'libx264', '-preset', 'medium', '-crf', '28', '-c:a', 'copy'],
                'description': 'Medium preset with CRF 28 for good compression'
            },
            {
                'name': 'Target Bitrate',
                'args': ['-c:v', 'libx264', '-preset', 'fast', '-b:v', '300k', '-maxrate', '400k', '-bufsize', '600k', '-c:a', 'copy'],
                'description': 'Target 300kbps video bitrate (similar to original)'
            },
            {
                'name': 'Hardware Accelerated',
                'args': ['-c:v', 'h264_videotoolbox', '-b:v', '350k', '-c:a', 'copy'],
                'description': 'macOS hardware encoding with 350kbps target'
            },
            {
                'name': 'Web Optimized',
                'args': ['-c:v', 'libx264', '-preset', 'fast', '-crf', '28', '-tune', 'film', 
                        '-profile:v', 'high', '-level', '4.0', '-movflags', '+faststart', '-c:a', 'copy'],
                'description': 'Web/mobile optimized with fast start'
            },
            {
                'name': 'Ultra Compressed',
                'args': ['-c:v', 'libx264', '-preset', 'medium', '-crf', '30', '-tune', 'film', '-c:a', 'copy'],
                'description': 'Maximum compression with acceptable quality'
            }
        ]
        
        # Run tests
        for config in test_configs:
            self.test_compression_settings(config['name'], config['args'], config['description'])
            time.sleep(0.5)  # Brief pause between tests
        
        # Generate report
        self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive optimization report"""
        print("\n" + "=" * 60)
        print("📊 COMPRESSION OPTIMIZATION RESULTS")
        print("=" * 60)
        
        if not self.results:
            print("❌ No successful tests to report")
            return
        
        # Sort by file size
        sorted_results = sorted(self.results, key=lambda x: x['file_size_mb'])
        
        print(f"\n{'Method':<20} {'Size (MB)':<10} {'Time (s)':<10} {'Reduction':<12} {'Bitrate':<10}")
        print("-" * 70)
        
        for result in sorted_results:
            print(f"{result['name']:<20} {result['file_size_mb']:<10.1f} "
                  f"{result['processing_time']:<10.1f} {result['size_reduction_percent']:<12.1f}% "
                  f"{result['bitrate_kbps']:<10}kbps")
        
        # Find best options
        best_compression = min(self.results, key=lambda x: x['file_size_mb'])
        best_speed = min(self.results, key=lambda x: x['processing_time'])
        
        # Find balanced option (good compression with reasonable speed)
        balanced_options = [r for r in self.results if r['processing_time'] < 8.0 and r['file_size_mb'] < 50.0]
        best_balanced = min(balanced_options, key=lambda x: x['file_size_mb']) if balanced_options else None
        
        print(f"\n🏆 RECOMMENDATIONS:")
        print(f"   🗜️  Best Compression: {best_compression['name']} ({best_compression['file_size_mb']:.1f}MB)")
        print(f"   ⚡ Fastest: {best_speed['name']} ({best_speed['processing_time']:.1f}s)")
        
        if best_balanced:
            print(f"   ⚖️  Best Balanced: {best_balanced['name']} ({best_balanced['file_size_mb']:.1f}MB, {best_balanced['processing_time']:.1f}s)")
            
            # Generate updated FFmpeg command
            print(f"\n🔧 RECOMMENDED FFMPEG SETTINGS:")
            print(f"   {' '.join(best_balanced['ffmpeg_args'])}")
        
        # Save detailed results
        with open('compression_results.json', 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to compression_results.json")

def main():
    if not os.path.exists('guitar_practice.mp4'):
        print("❌ Error: guitar_practice.mp4 not found!")
        print("   Please ensure the original video file is in the current directory.")
        sys.exit(1)
    
    optimizer = CompressionOptimizer()
    optimizer.run_comprehensive_tests()

if __name__ == "__main__":
    main()
