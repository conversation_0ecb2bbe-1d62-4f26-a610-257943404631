[{"name": "Current Settings", "description": "Current ultrafast preset with CRF 23", "processing_time": 3.2793710231781006, "file_size_mb": 112.72832298278809, "compression_ratio": 0.2025966350065997, "size_reduction_percent": -393.5916136847113, "bitrate_kbps": 1542, "ffmpeg_args": ["-c:v", "libx264", "-preset", "ultrafast", "-crf", "23", "-c:a", "copy"], "success": true}, {"name": "Balanced Fast", "description": "Fast preset with higher CRF for better compression", "processing_time": 10.75649118423462, "file_size_mb": 27.99744415283203, "compression_ratio": 0.815730849629709, "size_reduction_percent": -22.589454653325646, "bitrate_kbps": 383, "ffmpeg_args": ["-c:v", "libx264", "-preset", "fast", "-crf", "26", "-c:a", "copy"], "success": true}, {"name": "Optimized Medium", "description": "Medium preset with CRF 28 for good compression", "processing_time": 11.72894811630249, "file_size_mb": 24.52313804626465, "compression_ratio": 0.9312992025394046, "size_reduction_percent": -7.37687708453596, "bitrate_kbps": 335, "ffmpeg_args": ["-c:v", "libx264", "-preset", "medium", "-crf", "28", "-c:a", "copy"], "success": true}, {"name": "Target Bitrate", "description": "Target 300kbps video bitrate (similar to original)", "processing_time": 11.704961061477661, "file_size_mb": 31.656150817871094, "compression_ratio": 0.721451544682333, "size_reduction_percent": -38.60944749107391, "bitrate_kbps": 433, "ffmpeg_args": ["-c:v", "libx264", "-preset", "fast", "-b:v", "300k", "-maxrate", "400k", "-buf<PERSON>ze", "600k", "-c:a", "copy"], "success": true}, {"name": "Hardware Accelerated", "description": "macOS hardware encoding with 350kbps target", "processing_time": 14.249000072479248, "file_size_mb": 35.366167068481445, "compression_ratio": 0.6457691290669638, "size_reduction_percent": -54.8541041974002, "bitrate_kbps": 483, "ffmpeg_args": ["-c:v", "h264_videotoolbox", "-b:v", "350k", "-c:a", "copy"], "success": true}, {"name": "Web Optimized", "description": "Web/mobile optimized with fast start", "processing_time": 10.965221881866455, "file_size_mb": 24.824827194213867, "compression_ratio": 0.9199813850697472, "size_reduction_percent": -8.697851524918221, "bitrate_kbps": 339, "ffmpeg_args": ["-c:v", "libx264", "-preset", "fast", "-crf", "28", "-tune", "film", "-profile:v", "high", "-level", "4.0", "-movflags", "+faststart", "-c:a", "copy"], "success": true}, {"name": "Ultra Compressed", "description": "Maximum compression with acceptable quality", "processing_time": 11.170593023300171, "file_size_mb": 21.918728828430176, "compression_ratio": 1.0419572724777255, "size_reduction_percent": 4.026774761881857, "bitrate_kbps": 299, "ffmpeg_args": ["-c:v", "libx264", "-preset", "medium", "-crf", "30", "-tune", "film", "-c:a", "copy"], "success": true}]