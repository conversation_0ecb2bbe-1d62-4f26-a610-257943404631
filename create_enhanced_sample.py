#!/usr/bin/env python3
"""
Create Enhanced Sample Data

Takes the existing guitar_analysis.json and enhances it with sample
confidence scores and audio analysis data to test the Phase 1 implementation.
"""

import json
import random

def load_original_data():
    """Load the original guitar analysis data"""
    try:
        with open('guitar_analysis.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ Error: guitar_analysis.json not found!")
        return None

def enhance_with_sample_data(data):
    """Enhance existing data with sample confidence scores and audio analysis"""
    
    # Add enhanced metadata
    enhanced_data = {
        "analysis_metadata": {
            "version": "2.0_enhanced",
            "analysis_date": "2025-01-18",
            "total_duration": "561.0",
            "analysis_confidence": 92,
            "audio_analysis_enabled": True,
            "enhancement_features": [
                "confidence_scores",
                "audio_quality_analysis", 
                "enhanced_coaching_recommendations"
            ]
        },
        "chord_attempts": []
    }
    
    # Enhance each chord attempt
    for attempt in data['chord_attempts']:
        # Generate realistic sample values based on existing accuracy
        base_accuracy = attempt.get('visual_accuracy') or attempt.get('accuracy', 85)
        if base_accuracy is None:
            base_accuracy = random.randint(75, 95)  # Generate realistic sample accuracy
        
        enhanced_attempt = {
            "timestamp": attempt['timestamp'],
            "chord_attempted": attempt['chord_attempted'],
            "visual_accuracy": base_accuracy,
            "visual_confidence": min(95, base_accuracy + random.randint(5, 15)),
            "audio_quality": max(60, base_accuracy + random.randint(-10, 10)),
            "audio_confidence": min(90, base_accuracy + random.randint(0, 10)),
            "audio_feedback": generate_audio_feedback(base_accuracy),
            "finger_positions": generate_finger_positions(attempt['chord_attempted']),
            "audio_analysis": generate_audio_analysis(base_accuracy),
            "coaching_tip": attempt.get('coaching_tip', f"Focus on {attempt['chord_attempted']} chord placement"),
            "coaching_priority": determine_priority(base_accuracy),
            "improvement_areas": generate_improvement_areas(base_accuracy, attempt['chord_attempted'])
        }
        
        enhanced_data['chord_attempts'].append(enhanced_attempt)
    
    # Add session summary
    enhanced_data['session_summary'] = generate_session_summary(enhanced_data['chord_attempts'])
    
    # Add coaching recommendations
    enhanced_data['coaching_recommendations'] = generate_coaching_recommendations(enhanced_data['chord_attempts'])
    
    return enhanced_data

def generate_audio_feedback(accuracy):
    """Generate realistic audio feedback based on accuracy"""
    if accuracy >= 90:
        return random.choice([
            "Excellent tone and clarity",
            "Perfect string separation",
            "Rich harmonics and clean sound"
        ])
    elif accuracy >= 75:
        return random.choice([
            "Good tone with minor string muting",
            "Clear sound with slight finger noise",
            "Solid technique with room for improvement"
        ])
    else:
        return random.choice([
            "Muted strings detected - arch fingers more",
            "Fret buzz present - check finger pressure",
            "Unclear chord tone - adjust finger placement"
        ])

def generate_finger_positions(chord):
    """Generate sample finger position data"""
    chord_patterns = {
        "G Major": {
            "finger_2": {"fret": 3, "string": 6, "correct": True, "confidence": 90},
            "finger_1": {"fret": 2, "string": 5, "correct": True, "confidence": 85},
            "finger_3": {"fret": 3, "string": 1, "correct": True, "confidence": 88}
        },
        "D Major": {
            "finger_1": {"fret": 2, "string": 1, "correct": True, "confidence": 90},
            "finger_2": {"fret": 2, "string": 3, "correct": True, "confidence": 85},
            "finger_3": {"fret": 3, "string": 2, "correct": True, "confidence": 80}
        },
        "C Major": {
            "finger_1": {"fret": 1, "string": 2, "correct": True, "confidence": 92},
            "finger_2": {"fret": 2, "string": 4, "correct": True, "confidence": 88},
            "finger_3": {"fret": 3, "string": 5, "correct": True, "confidence": 85}
        }
    }
    
    return chord_patterns.get(chord, {
        "finger_1": {"fret": 2, "string": 3, "correct": True, "confidence": 85}
    })

def generate_audio_analysis(accuracy):
    """Generate sample audio analysis data"""
    base_clarity = max(60, accuracy + random.randint(-5, 5))
    
    return {
        "string_clarity": {
            "string_1": min(95, base_clarity + random.randint(-5, 10)),
            "string_2": min(95, base_clarity + random.randint(-5, 10)),
            "string_3": min(95, base_clarity + random.randint(-5, 10)),
            "string_4": min(95, base_clarity + random.randint(-5, 10)),
            "string_5": min(95, base_clarity + random.randint(-5, 10)),
            "string_6": min(95, base_clarity + random.randint(-5, 10))
        },
        "fret_buzz_detected": accuracy < 70,
        "strum_technique": {
            "quality": min(95, accuracy + random.randint(-5, 10)),
            "consistency": min(90, accuracy + random.randint(-10, 5)),
            "confidence": min(90, accuracy + random.randint(0, 10))
        },
        "overall_tone_quality": min(95, accuracy + random.randint(-5, 10))
    }

def determine_priority(accuracy):
    """Determine coaching priority based on accuracy"""
    if accuracy >= 85:
        return "low"
    elif accuracy >= 70:
        return "medium"
    else:
        return "high"

def generate_improvement_areas(accuracy, chord):
    """Generate improvement areas based on accuracy and chord"""
    areas = []
    
    if accuracy < 80:
        areas.append("finger_placement")
    if accuracy < 70:
        areas.append("string_clearance")
    if chord in ["D Major", "C Major"] and accuracy < 85:
        areas.append("finger_arch")
    
    return areas if areas else []

def generate_session_summary(attempts):
    """Generate session summary from attempts"""
    total_attempts = len(attempts)
    avg_visual = sum(a['visual_accuracy'] for a in attempts) / total_attempts
    avg_audio = sum(a['audio_quality'] for a in attempts) / total_attempts
    
    # Find strongest chord
    chord_scores = {}
    for attempt in attempts:
        chord = attempt['chord_attempted']
        if chord not in chord_scores:
            chord_scores[chord] = []
        chord_scores[chord].append(attempt['visual_accuracy'])
    
    strongest_chord = max(chord_scores.keys(), 
                         key=lambda c: sum(chord_scores[c]) / len(chord_scores[c]))
    
    return {
        "total_attempts": total_attempts,
        "average_visual_accuracy": round(avg_visual, 1),
        "average_audio_quality": round(avg_audio, 1),
        "average_visual_confidence": round(avg_visual + 5, 1),
        "average_audio_confidence": round(avg_audio + 3, 1),
        "most_common_issue": "finger_arch_positioning",
        "strongest_chord": strongest_chord,
        "improvement_focus": "Focus on consistent finger placement and string clearance",
        "overall_session_quality": "good" if avg_visual >= 80 else "fair",
        "session_confidence": min(95, int(avg_visual + 10))
    }

def generate_coaching_recommendations(attempts):
    """Generate coaching recommendations"""
    avg_accuracy = sum(a['visual_accuracy'] for a in attempts) / len(attempts)
    
    return {
        "immediate_focus": [
            "Practice chord transitions slowly",
            "Focus on finger arch and string clearance",
            "Work on consistent finger pressure"
        ],
        "practice_suggestions": [
            "Spend 5 minutes daily on chord formation",
            "Practice with a metronome for timing",
            "Record yourself to monitor progress"
        ],
        "strengths_identified": [
            "Good chord recognition",
            "Consistent practice approach",
            "Steady improvement trend"
        ],
        "confidence_level": min(95, int(avg_accuracy + 10))
    }

def main():
    """Create enhanced sample data for testing"""
    print("🎸 Creating Enhanced Sample Data for Phase 1 Testing")
    print("=" * 60)
    
    original_data = load_original_data()
    if not original_data:
        return
    
    print(f"✓ Loaded original data: {len(original_data['chord_attempts'])} attempts")
    
    enhanced_data = enhance_with_sample_data(original_data)
    
    # Save enhanced sample data
    with open('guitar_analysis_enhanced_sample.json', 'w') as f:
        json.dump(enhanced_data, f, indent=2)
    
    print(f"✓ Created enhanced sample data: guitar_analysis_enhanced_sample.json")
    print(f"📊 Enhanced features:")
    print(f"   • Confidence scores for all attempts")
    print(f"   • Audio quality analysis")
    print(f"   • Enhanced coaching recommendations")
    print(f"   • Session summary and analytics")
    
    print(f"\n🚀 Ready for Phase 1 testing!")
    print(f"   Use this file to test the enhanced video processing pipeline")

if __name__ == "__main__":
    main()
