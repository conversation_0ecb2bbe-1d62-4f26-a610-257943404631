# Enhanced Guitar Analysis Prompt for Gemini

## Phase 1 Implementation: Confidence Scores + Audio Analysis

````
Analyze this guitar practice video with enhanced detail, confidence scoring, and audio analysis:

## CRITICAL ANALYSIS REQUIREMENTS - PROVIDE ACTUAL VALUES, NOT NULL:

**IMPORTANT:** You MUST provide actual numerical values (0-100) for ALL fields. Do NOT use null, None, or placeholder values. Analyze the video content and provide realistic assessments based on what you observe.

### 1. CHORD ANALYSIS WITH CONFIDENCE SCORES
- Identify each chord attempt with timestamp (0.5-second granularity)
- **REQUIRED:** Provide visual accuracy score (0-100%) based on finger positioning quality you observe
- **REQUIRED:** Include visual confidence score (0-100%) based on video clarity and your analysis certainty
- **REQUIRED:** Assess finger placement correctness for each finger with individual confidence scores (0-100%)
- **SCORING GUIDANCE:**
  - Visual accuracy: 90-100% = perfect positioning, 80-89% = good with minor issues, 70-79% = fair with noticeable problems, <70% = poor positioning
  - Visual confidence: 90-100% = clear video/high certainty, 80-89% = good visibility, 70-79% = some uncertainty, <70% = poor video quality

### 2. AUDIO QUALITY ANALYSIS
- **REQUIRED:** Evaluate overall audio quality score (0-100%) based on chord tone clarity you hear
- **REQUIRED:** Provide audio confidence score (0-100%) based on audio clarity and your analysis certainty
- **REQUIRED:** Assess string clarity for each individual string (0-100%) - listen for muted or unclear strings
- **REQUIRED:** Detect fret buzz (true/false), muted strings, or other audio issues you can hear
- **REQUIRED:** Evaluate strum technique quality (0-100%) and consistency (0-100%)
- **REQUIRED:** Provide specific audio feedback text describing what you hear and how to improve
- **SCORING GUIDANCE:**
  - Audio quality: 90-100% = crystal clear tone, 80-89% = good tone with minor issues, 70-79% = fair tone, <70% = poor/muted tone
  - String clarity: 90-100% = string rings clearly, 80-89% = mostly clear, 70-79% = somewhat muted, <70% = very muted/buzzing
  - Audio confidence: Based on recording quality and your ability to assess the audio clearly

### 3. ENHANCED COACHING RECOMMENDATIONS
- **REQUIRED:** Generate specific coaching tips with priority levels (low/medium/high) based on observed issues
- **REQUIRED:** Identify improvement areas with actionable suggestions (e.g., "finger_placement", "string_clearance", "finger_arch")
- **REQUIRED:** Combine visual and audio feedback for comprehensive coaching
- **REQUIRED:** Provide confidence scores (0-100%) for coaching recommendations
- **PRIORITY GUIDANCE:**
  - High: Fundamental issues affecting chord quality (accuracy <70%)
  - Medium: Noticeable issues that could be improved (accuracy 70-85%)
  - Low: Minor refinements for already good technique (accuracy >85%)

### 4. SESSION ANALYSIS
- **REQUIRED:** Calculate average scores across all categories using actual numbers
- **REQUIRED:** Identify strongest and weakest chord performances based on accuracy scores
- **REQUIRED:** Suggest focused practice areas based on analysis patterns you observe
- **REQUIRED:** Provide overall session quality assessment ("poor"/"fair"/"good"/"excellent")
- **REQUIRED:** Generate session confidence score (0-100%) based on overall analysis quality

## EXAMPLE ANALYSIS APPROACH:

**For each chord attempt you observe:**
1. **Visual Assessment:** Look at finger placement, hand position, chord formation
2. **Audio Assessment:** Listen to chord tone, string clarity, any buzzing or muting
3. **Assign Realistic Scores:** Use the full 0-100 range based on what you observe
4. **Provide Specific Feedback:** Describe exactly what you see/hear and how to improve

**EXAMPLE SCORING:**
- Perfect G Major with clear fingers and tone: visual_accuracy=95, audio_quality=92
- G Major with slight finger muting: visual_accuracy=82, audio_quality=75
- Poor G Major with multiple muted strings: visual_accuracy=65, audio_quality=55

## REQUIRED JSON OUTPUT FORMAT - ALL FIELDS MUST HAVE ACTUAL VALUES:

```json
{
    "analysis_metadata": {
        "version": "2.0_enhanced",
        "analysis_date": "YYYY-MM-DD",
        "total_duration": "duration_in_seconds",
        "analysis_confidence": 0-100,
        "audio_analysis_enabled": true,
        "enhancement_features": ["confidence_scores", "audio_quality_analysis", "enhanced_coaching_recommendations"]
    },
    "chord_attempts": [
        {
            "timestamp": "M:SS.S",
            "chord_attempted": "chord_name",
            "visual_accuracy": 85,  // EXAMPLE: Actual number 0-100, NOT null
            "visual_confidence": 90,  // EXAMPLE: Actual number 0-100, NOT null
            "audio_quality": 78,  // EXAMPLE: Actual number 0-100, NOT null
            "audio_confidence": 82,  // EXAMPLE: Actual number 0-100, NOT null
            "audio_feedback": "Clear tone with slight finger noise on B string",  // EXAMPLE: Specific description of what you hear
            "finger_positions": {
                "finger_N": {
                    "fret": number,
                    "string": number,
                    "correct": boolean,
                    "confidence": 0-100,
                    "issue": "optional_specific_issue"
                }
            },
            "audio_analysis": {
                "string_clarity": {
                    "string_1": 92,  // EXAMPLE: High E string clarity
                    "string_2": 88,  // EXAMPLE: B string clarity
                    "string_3": 90,  // EXAMPLE: G string clarity
                    "string_4": 85,  // EXAMPLE: D string clarity
                    "string_5": 87,  // EXAMPLE: A string clarity
                    "string_6": 83   // EXAMPLE: Low E string clarity
                },
                "fret_buzz_detected": false,  // EXAMPLE: true if you hear buzzing, false if clean
                "strum_technique": {
                    "quality": 85,      // EXAMPLE: Strum technique quality
                    "consistency": 80,  // EXAMPLE: Strum consistency
                    "confidence": 88    // EXAMPLE: Confidence in strum analysis
                },
                "overall_tone_quality": 87,  // EXAMPLE: Overall chord tone quality
                "issues": ["array_of_detected_issues"]
            },
            "coaching_tip": "specific_improvement_suggestion",
            "coaching_priority": "low/medium/high",
            "improvement_areas": ["array_of_focus_areas"]
        }
    ],
    "session_summary": {
        "total_attempts": number,
        "average_visual_accuracy": number,
        "average_audio_quality": number,
        "average_visual_confidence": number,
        "average_audio_confidence": number,
        "most_common_issue": "description",
        "strongest_chord": "chord_name",
        "improvement_focus": "primary_recommendation",
        "overall_session_quality": "poor/fair/good/excellent",
        "session_confidence": 0-100
    },
    "coaching_recommendations": {
        "immediate_focus": ["array_of_priority_items"],
        "practice_suggestions": ["array_of_practice_exercises"],
        "strengths_identified": ["array_of_positive_observations"],
        "confidence_level": 0-100
    }
}
````

## ANALYSIS GUIDELINES:

### Visual Analysis:

- Focus on finger placement accuracy, hand position, and chord formation
- Provide confidence scores based on video clarity and analysis certainty
- Identify specific finger positioning issues with targeted feedback

### Audio Analysis:

- Evaluate string clarity, tone quality, and overall sound
- Detect technical issues like fret buzz, muted strings, or poor strum technique
- Provide specific audio improvement suggestions
- Consider harmonic content and chord resonance

### Confidence Scoring:

- Visual confidence: Based on video quality, lighting, hand visibility
- Audio confidence: Based on audio clarity, background noise, recording quality
- Analysis confidence: Overall certainty in the assessment
- Use confidence scores to indicate reliability of feedback

### Coaching Integration:

- Combine visual and audio feedback for comprehensive coaching
- Prioritize recommendations based on impact and difficulty
- Provide specific, actionable improvement suggestions
- Maintain encouraging tone while being constructively critical

### Mobile Optimization:

- Ensure JSON structure is efficient for mobile processing
- Provide 0.5-second timestamp granularity for smooth overlay display
- Include all necessary data for professional UI overlay generation
- Maintain compatibility with existing mobile-optimized architecture

## CRITICAL REMINDERS FOR ACCURATE ANALYSIS:

### **VISUAL ANALYSIS GUIDELINES:**

- **90-100%:** Perfect finger placement, clean chord formation, proper hand position
- **80-89%:** Good technique with minor finger positioning issues
- **70-79%:** Fair technique with noticeable finger placement problems
- **60-69%:** Poor technique with multiple finger issues
- **<60%:** Very poor technique with major chord formation problems

### **AUDIO ANALYSIS GUIDELINES:**

- **90-100%:** Crystal clear chord tone, all strings ringing cleanly
- **80-89%:** Good tone with minor string muting or slight buzz
- **70-79%:** Fair tone with noticeable muted strings or buzz
- **60-69%:** Poor tone with multiple muted strings
- **<60%:** Very poor tone with heavy muting or significant buzz

### **CONFIDENCE SCORING GUIDELINES:**

- **Visual Confidence:** Based on video quality, lighting, hand visibility
- **Audio Confidence:** Based on audio clarity, background noise, recording quality
- **High (90-100%):** Clear video/audio, high certainty in analysis
- **Medium (70-89%):** Good quality, reasonable certainty
- **Low (<70%):** Poor quality, uncertain analysis

### **MANDATORY REQUIREMENTS:**

1. **NO NULL VALUES:** Every numerical field must have an actual number 0-100
2. **REALISTIC SCORING:** Use the full range based on what you observe
3. **SPECIFIC FEEDBACK:** Provide detailed, actionable coaching suggestions
4. **CONSISTENT ANALYSIS:** Maintain scoring consistency across all attempts
5. **ACTUAL OBSERVATION:** Base scores on what you actually see and hear in the video

### **EXAMPLE COMPLETE ANALYSIS:**

```json
{
  "timestamp": "1:23.5",
  "chord_attempted": "G Major",
  "visual_accuracy": 85,
  "visual_confidence": 92,
  "audio_quality": 78,
  "audio_confidence": 85,
  "audio_feedback": "Good chord tone with slight muting on B string - arch finger 3 more",
  "coaching_tip": "Excellent G Major! Try arching your third finger slightly more to avoid touching the B string.",
  "coaching_priority": "low",
  "improvement_areas": ["finger_arch"]
}
```

This enhanced analysis will provide professional-level guitar instruction with confidence-scored feedback and comprehensive audio analysis while maintaining mobile deployment optimization.

**REMEMBER: Provide actual numerical values for ALL fields. Your analysis should be based on what you actually observe in the video and hear in the audio.**

```

```
