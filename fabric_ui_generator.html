<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guitar Coaching - Fabric.js UI Generator</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .controls {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .canvas-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        canvas {
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: transform 0.2s;
        }
        
        .button:hover {
            transform: translateY(-2px);
        }
        
        .button.secondary {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }
        
        .button.danger {
            background: linear-gradient(135deg, #F44336, #D32F2F);
        }
        
        .chord-selector {
            margin: 10px;
        }
        
        select, input {
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            margin: 5px;
        }
        
        select option {
            background: #333;
            color: white;
        }
        
        .export-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .preview-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
        }
        
        .preview-canvas {
            border-radius: 8px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎸 Guitar Coaching - Modern UI Generator</h1>
            <p>Fabric.js + Canvas API Proof of Concept</p>
        </div>
        
        <div class="controls">
            <h3>🎨 Design Controls</h3>
            
            <div class="chord-selector">
                <label>Chord:</label>
                <select id="chordSelect">
                    <option value="C Major">C Major</option>
                    <option value="D Major">D Major</option>
                    <option value="E Minor">E Minor</option>
                    <option value="G Major">G Major</option>
                    <option value="A Minor">A Minor</option>
                    <option value="F Major">F Major</option>
                </select>
                
                <label>Score:</label>
                <input type="range" id="scoreSlider" min="0" max="100" value="85">
                <span id="scoreValue">85%</span>
            </div>
            
            <div class="chord-selector">
                <label>Coaching Tip:</label>
                <input type="text" id="coachingTip" value="Great chord! Keep your fingers arched." style="width: 300px;">
            </div>
            
            <div class="chord-selector">
                <label>UI Style:</label>
                <select id="styleSelect">
                    <option value="modern">Modern Card</option>
                    <option value="glassmorphism">Glassmorphism</option>
                    <option value="minimal">Minimal Clean</option>
                    <option value="premium">Premium Dark</option>
                </select>
            </div>
            
            <button class="button" onclick="generateOverlay()">🎨 Generate Overlay</button>
            <button class="button secondary" onclick="animateOverlay()">✨ Animate</button>
            <button class="button danger" onclick="clearCanvas()">🗑️ Clear</button>
        </div>
        
        <div class="canvas-container">
            <h3>📱 Live Preview (Mobile UI)</h3>
            <canvas id="coachingCanvas" width="640" height="360"></canvas>
        </div>
        
        <div class="export-section">
            <h3>📤 Export Options</h3>
            <button class="button" onclick="exportPNG()">💾 Export PNG</button>
            <button class="button secondary" onclick="generateFrameSequence()">🎬 Generate Frame Sequence</button>
            <button class="button" onclick="showVideoIntegration()">🎥 Video Integration Demo</button>
            
            <div id="exportResults" style="margin-top: 20px;"></div>
        </div>
        
        <div class="preview-grid" id="previewGrid">
            <!-- Generated previews will appear here -->
        </div>
    </div>

    <script>
        let canvas;
        let currentOverlay = null;
        
        // Initialize Fabric.js canvas
        function initCanvas() {
            canvas = new fabric.Canvas('coachingCanvas', {
                backgroundColor: 'rgba(0, 0, 0, 0.1)'
            });
            
            // Add video background simulation
            const videoBg = new fabric.Rect({
                left: 0,
                top: 0,
                width: 640,
                height: 360,
                fill: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)',
                selectable: false,
                evented: false
            });
            canvas.add(videoBg);
        }
        
        // Get score color based on accuracy
        function getScoreColor(score) {
            if (score >= 80) return '#4CAF50'; // Material Green
            if (score >= 60) return '#FF9800'; // Material Orange
            return '#F44336'; // Material Red
        }
        
        // Get score background color
        function getScoreBgColor(score) {
            if (score >= 80) return 'rgba(76, 175, 80, 0.2)';
            if (score >= 60) return 'rgba(255, 152, 0, 0.2)';
            return 'rgba(244, 67, 54, 0.2)';
        }
        
        // Create modern card style overlay
        function createModernCardOverlay(chord, score, tip) {
            const group = new fabric.Group([], {
                left: 30,
                top: 30,
                selectable: true
            });
            
            // Status card background
            const statusCard = new fabric.Rect({
                width: 280,
                height: 70,
                fill: 'rgba(44, 44, 44, 0.9)',
                rx: 12,
                ry: 12,
                shadow: new fabric.Shadow({
                    color: 'rgba(0, 0, 0, 0.3)',
                    blur: 8,
                    offsetX: 0,
                    offsetY: 4
                })
            });
            
            // Chord text with musical note
            const chordText = new fabric.Text(`♪ ${chord}`, {
                left: 20,
                top: 20,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 24,
                fill: '#FFFFFF',
                fontWeight: 'bold'
            });
            
            // Score badge background
            const scoreBg = new fabric.Rect({
                left: 200,
                top: 15,
                width: 60,
                height: 25,
                fill: getScoreColor(score),
                rx: 12,
                ry: 12
            });
            
            // Score text
            const scoreText = new fabric.Text(`${score}%`, {
                left: 215,
                top: 20,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 14,
                fill: '#FFFFFF',
                fontWeight: 'bold'
            });
            
            // Accent line
            const accentLine = new fabric.Rect({
                left: 0,
                top: 75,
                width: 280,
                height: 2,
                fill: getScoreColor(score)
            });
            
            group.addWithUpdate(statusCard);
            group.addWithUpdate(chordText);
            group.addWithUpdate(scoreBg);
            group.addWithUpdate(scoreText);
            group.addWithUpdate(accentLine);
            
            // Coaching tip card (bottom)
            const tipCard = new fabric.Rect({
                left: 30,
                top: 280,
                width: 580,
                height: 50,
                fill: 'rgba(30, 30, 30, 0.9)',
                rx: 8,
                ry: 8,
                shadow: new fabric.Shadow({
                    color: 'rgba(0, 0, 0, 0.3)',
                    blur: 6,
                    offsetX: 0,
                    offsetY: 2
                })
            });
            
            // Tip icon
            const tipIcon = new fabric.Text('💡', {
                left: 45,
                top: 295,
                fontSize: 18
            });
            
            // Tip text
            const tipText = new fabric.Text(tip, {
                left: 75,
                top: 300,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 14,
                fill: '#E0E0E0',
                width: 520
            });
            
            return [group, tipCard, tipIcon, tipText];
        }
        
        // Create glassmorphism style overlay
        function createGlassmorphismOverlay(chord, score, tip) {
            const elements = [];
            
            // Glass card
            const glassCard = new fabric.Rect({
                left: 25,
                top: 25,
                width: 300,
                height: 80,
                fill: 'rgba(255, 255, 255, 0.1)',
                stroke: 'rgba(255, 255, 255, 0.2)',
                strokeWidth: 1,
                rx: 16,
                ry: 16,
                shadow: new fabric.Shadow({
                    color: 'rgba(0, 0, 0, 0.1)',
                    blur: 20,
                    offsetX: 0,
                    offsetY: 8
                })
            });
            
            // Chord text with glow effect
            const chordText = new fabric.Text(`♪ ${chord}`, {
                left: 45,
                top: 45,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 28,
                fill: '#FFFFFF',
                fontWeight: '300',
                shadow: new fabric.Shadow({
                    color: getScoreColor(score),
                    blur: 10,
                    offsetX: 0,
                    offsetY: 0
                })
            });
            
            // Floating score badge
            const scoreCircle = new fabric.Circle({
                left: 270,
                top: 35,
                radius: 20,
                fill: getScoreColor(score),
                shadow: new fabric.Shadow({
                    color: getScoreColor(score),
                    blur: 15,
                    offsetX: 0,
                    offsetY: 0
                })
            });
            
            const scoreText = new fabric.Text(`${score}`, {
                left: 280,
                top: 45,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 12,
                fill: '#FFFFFF',
                fontWeight: 'bold',
                originX: 'center',
                originY: 'center'
            });
            
            elements.push(glassCard, chordText, scoreCircle, scoreText);
            
            // Glass tip card
            const tipGlass = new fabric.Rect({
                left: 25,
                top: 270,
                width: 590,
                height: 60,
                fill: 'rgba(255, 255, 255, 0.08)',
                stroke: 'rgba(255, 255, 255, 0.15)',
                strokeWidth: 1,
                rx: 12,
                ry: 12
            });
            
            const tipText = new fabric.Text(tip, {
                left: 45,
                top: 290,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 16,
                fill: '#FFFFFF',
                fontWeight: '300'
            });
            
            elements.push(tipGlass, tipText);
            
            return elements;
        }
        
        // Generate overlay based on selected style
        function generateOverlay() {
            const chord = document.getElementById('chordSelect').value;
            const score = parseInt(document.getElementById('scoreSlider').value);
            const tip = document.getElementById('coachingTip').value;
            const style = document.getElementById('styleSelect').value;
            
            // Clear previous overlay
            if (currentOverlay) {
                currentOverlay.forEach(obj => canvas.remove(obj));
            }
            
            let elements = [];
            
            switch (style) {
                case 'modern':
                    elements = createModernCardOverlay(chord, score, tip);
                    break;
                case 'glassmorphism':
                    elements = createGlassmorphismOverlay(chord, score, tip);
                    break;
                case 'minimal':
                    elements = createMinimalOverlay(chord, score, tip);
                    break;
                case 'premium':
                    elements = createPremiumOverlay(chord, score, tip);
                    break;
            }
            
            elements.forEach(element => canvas.add(element));
            currentOverlay = elements;
            canvas.renderAll();
        }
        
        // Create minimal clean overlay
        function createMinimalOverlay(chord, score, tip) {
            const elements = [];
            
            const chordText = new fabric.Text(`♪ ${chord}`, {
                left: 40,
                top: 40,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 32,
                fill: '#FFFFFF',
                fontWeight: '200'
            });
            
            const scoreText = new fabric.Text(`${score}%`, {
                left: 40,
                top: 80,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 24,
                fill: getScoreColor(score),
                fontWeight: '300'
            });
            
            const tipText = new fabric.Text(tip, {
                left: 40,
                top: 300,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 16,
                fill: '#E0E0E0',
                fontWeight: '300'
            });
            
            elements.push(chordText, scoreText, tipText);
            return elements;
        }
        
        // Create premium dark overlay
        function createPremiumOverlay(chord, score, tip) {
            const elements = [];
            
            // Premium dark card
            const premiumCard = new fabric.Rect({
                left: 20,
                top: 20,
                width: 320,
                height: 90,
                fill: 'rgba(18, 18, 18, 0.95)',
                stroke: getScoreColor(score),
                strokeWidth: 2,
                rx: 8,
                ry: 8,
                shadow: new fabric.Shadow({
                    color: getScoreColor(score),
                    blur: 20,
                    offsetX: 0,
                    offsetY: 0
                })
            });
            
            const chordText = new fabric.Text(`♪ ${chord}`, {
                left: 40,
                top: 45,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 26,
                fill: '#FFFFFF',
                fontWeight: 'bold'
            });
            
            const scoreText = new fabric.Text(`${score}%`, {
                left: 40,
                top: 75,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 18,
                fill: getScoreColor(score),
                fontWeight: 'bold'
            });
            
            elements.push(premiumCard, chordText, scoreText);
            
            // Premium tip section
            const tipSection = new fabric.Rect({
                left: 20,
                top: 260,
                width: 600,
                height: 70,
                fill: 'rgba(18, 18, 18, 0.9)',
                stroke: 'rgba(255, 255, 255, 0.1)',
                strokeWidth: 1,
                rx: 6,
                ry: 6
            });
            
            const tipLabel = new fabric.Text('COACHING TIP', {
                left: 40,
                top: 275,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 10,
                fill: getScoreColor(score),
                fontWeight: 'bold',
                letterSpacing: 1
            });
            
            const tipText = new fabric.Text(tip, {
                left: 40,
                top: 295,
                fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
                fontSize: 16,
                fill: '#E0E0E0',
                fontWeight: '400'
            });
            
            elements.push(tipSection, tipLabel, tipText);
            return elements;
        }
        
        // Animate overlay entrance
        function animateOverlay() {
            if (!currentOverlay) return;
            
            currentOverlay.forEach((obj, index) => {
                obj.set('opacity', 0);
                obj.animate('opacity', 1, {
                    duration: 500,
                    delay: index * 100,
                    easing: fabric.util.ease.easeOutCubic,
                    onChange: canvas.renderAll.bind(canvas)
                });
            });
        }
        
        // Clear canvas
        function clearCanvas() {
            if (currentOverlay) {
                currentOverlay.forEach(obj => canvas.remove(obj));
                currentOverlay = null;
            }
            canvas.renderAll();
        }
        
        // Export as PNG
        function exportPNG() {
            const dataURL = canvas.toDataURL('image/png');
            const link = document.createElement('a');
            link.download = 'guitar-coaching-overlay.png';
            link.href = dataURL;
            link.click();
            
            document.getElementById('exportResults').innerHTML = 
                '<p style="color: #4CAF50;">✅ PNG exported successfully!</p>';
        }
        
        // Generate frame sequence for video
        function generateFrameSequence() {
            const frames = [];
            const chords = ['C Major', 'D Major', 'E Minor', 'G Major'];
            const scores = [95, 87, 100, 92];
            const tips = [
                'Excellent C major! Very clean.',
                'Good D major. Watch finger placement.',
                'Perfect E minor. Great job!',
                'Solid G major. Nice tone.'
            ];
            
            document.getElementById('exportResults').innerHTML = 
                '<p>🎬 Generating frame sequence...</p>';
            
            setTimeout(() => {
                const previewGrid = document.getElementById('previewGrid');
                previewGrid.innerHTML = '';
                
                chords.forEach((chord, index) => {
                    // Create mini canvas for preview
                    const miniCanvas = document.createElement('canvas');
                    miniCanvas.width = 320;
                    miniCanvas.height = 180;
                    miniCanvas.className = 'preview-canvas';
                    
                    const fabricMini = new fabric.Canvas(miniCanvas);
                    
                    // Add background
                    const bg = new fabric.Rect({
                        width: 320,
                        height: 180,
                        fill: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
                    });
                    fabricMini.add(bg);
                    
                    // Add scaled overlay
                    const elements = createModernCardOverlay(chord, scores[index], tips[index]);
                    elements.forEach(element => {
                        if (element.scaleToWidth) {
                            element.scaleToWidth(element.width * 0.5);
                        } else {
                            element.scale(0.5);
                        }
                        fabricMini.add(element);
                    });
                    
                    // Create preview item
                    const previewItem = document.createElement('div');
                    previewItem.className = 'preview-item';
                    previewItem.innerHTML = `
                        <div>${miniCanvas.outerHTML}</div>
                        <p>Frame ${index + 1}: ${chord} (${scores[index]}%)</p>
                    `;
                    
                    previewGrid.appendChild(previewItem);
                });
                
                document.getElementById('exportResults').innerHTML = 
                    '<p style="color: #4CAF50;">✅ Frame sequence generated! Ready for video integration.</p>';
            }, 1000);
        }
        
        // Show video integration demo
        function showVideoIntegration() {
            document.getElementById('exportResults').innerHTML = `
                <h4>🎥 Video Integration Options:</h4>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h5>Option 1: WebCodecs API (Modern)</h5>
                    <pre style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 4px; font-size: 12px;">
// Generate overlay frames with Fabric.js
const overlayFrames = await generateCoachingFrames(chordData);

// Combine with original video using WebCodecs
const encoder = new VideoEncoder({
    output: (chunk) => outputVideo.push(chunk),
    error: (e) => console.error(e)
});

overlayFrames.forEach(frame => encoder.encode(frame));
                    </pre>
                </div>
                
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h5>Option 2: FFmpeg Integration (Compatible)</h5>
                    <pre style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 4px; font-size: 12px;">
// Export Fabric.js overlays as PNG sequence
const pngFrames = await exportFrameSequence();

// Use FFmpeg to composite with original video
ffmpeg -i original.mp4 -i overlay_%d.png 
       -filter_complex overlay=0:0 
       -c:a copy output.mp4
                    </pre>
                </div>
                
                <p style="color: #4CAF50;">🚀 Estimated processing time: 3-8 seconds (vs current 5.2s)</p>
                <p style="color: #FF9800;">💡 UI quality: Professional mobile app level</p>
            `;
        }
        
        // Update score display
        document.getElementById('scoreSlider').addEventListener('input', function() {
            document.getElementById('scoreValue').textContent = this.value + '%';
        });
        
        // Initialize when page loads
        window.addEventListener('load', function() {
            initCanvas();
            generateOverlay(); // Generate initial overlay
        });
    </script>
</body>
</html>
