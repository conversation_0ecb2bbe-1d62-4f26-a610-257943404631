# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a computer vision demo that analyzes basketball shooting performance using AI feedback. The project processes video footage to track a player's shots and displays real-time statistics and AI-generated coaching feedback.

## Architecture

- **ball.py**: Main OpenCV-based video processor that handles pose detection, shot tracking, and overlay rendering
- **ball.json**: AI-generated shot analysis data containing timestamps, results, and feedback (generated by Gemini AI)
- **final_ball.mov**: Source video file for processing
- Video files are processed at different resolutions for performance optimization

## Key Components

### Video Processing Pipeline
1. Uses MediaPipe for pose detection to track player head position
2. Processes frames at reduced framerate (3 FPS) for performance
3. Overlays real-time statistics and feedback on high-resolution display video
4. Renders visual elements: player name arrow, shot counters, and coaching feedback

### Shot Analysis System
- Shot data is pre-analyzed by Gemini AI and stored in `ball.json`
- Each shot entry contains: timestamp, result (made/missed), shot type, cumulative stats, and personalized feedback
- Feedback is displayed for 4 seconds after each shot with color-coded animations

## Dependencies

The project requires:
- OpenCV (`cv2`)
- MediaPipe (`mediapipe`)
- NumPy (`numpy`)
- Standard Python libraries: `json`, `datetime`, `textwrap`, `time`

## Running the Application

```bash
python ball.py
```

The script will:
1. Process the video file and apply shot analysis
2. Display a live preview window
3. Generate a final processed video as `final.mp4`
4. Press 'q' to quit the preview

## File Path Configuration

The video file paths are currently hardcoded in `ball.py`:
- Processing video: `/Users/<USER>/Developer/tidbit-script/final_ball.mov`
- Display video: `final_ball.mp4`

These paths need to be updated to match your local file structure.

## AI Integration Notes

The magic happens in `ball.json` - this contains Gemini AI's analysis of the basketball video. For real-time implementation, you would need to:
1. Send video frames to Gemini Video API (limited to 1 FPS)
2. Process AI responses in real-time
3. Integrate with live camera feed instead of pre-recorded video