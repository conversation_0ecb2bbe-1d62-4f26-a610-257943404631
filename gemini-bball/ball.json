// THIS IS THE MOST IMPORTANT THING.
// THIS IS GENERATED FROM GEMINI
// PROMPT HERE: https://x.com/FarzaTV/status/1928492138574262604
// you just need to tell it to output the json.

// YES YOU CAN USE THE GEMINI API FOR THIS
// I WAS JUST TOO LAZY TO DO IT MYSELF

{
    "shots": [
      {
        "timestamp_of_outcome": "0:07.5",
        "result": "missed",
        "shot_type": "Jump shot (around free-throw line)",
        "total_shots_made_so_far": 0,
        "total_shots_missed_so_far": 1,
        "total_layups_made_so_far": 0,
        "feedback": "You're pushing that ball, not shooting it; get your elbow under, extend fully, and follow through."
      },
      {
        "timestamp_of_outcome": "0:13.0",
        "result": "made",
        "shot_type": "Three-pointer",
        "total_shots_made_so_far": 1,
        "total_shots_missed_so_far": 1,
        "total_layups_made_so_far": 0,
        "feedback": "It went in, but watch that slight fade keep your shoulders square to the hoop through the whole motion."
      },
      {
        "timestamp_of_outcome": "0:21.5",
        "result": "made",
        "shot_type": "Layup",
        "total_shots_made_so_far": 2,
        "total_shots_missed_so_far": 1,
        "total_layups_made_so_far": 1,
        "feedback": "Drive that knee on the layup, protect the ball higher with your off-hand, and finish decisively."
      },
      {
        "timestamp_of_outcome": "0:28.5",
        "result": "made",
        "shot_type": "Jump shot (free-throw line)",
        "total_shots_made_so_far": 3,
        "total_shots_missed_so_far": 1,
        "total_layups_made_so_far": 1,
        "feedback": "Better balance, but that shot pocket and release point must be identical every single time for real consistency."
      }
    ]
  }