======================================
Guitar Positioning Tutor: Build Plan
======================================

.. contents::
   :local:

1. Project Overview
===================

This project is to create a **fully offline, real-time guitar coaching app** for iOS and Android using React Native. The app will act as a virtual tutor, providing instant, visual feedback on a user's finger positioning for various chords.

The core value proposition is to offer a powerful, AI-driven learning experience that is **completely private, works anywhere without an internet connection, and has zero ongoing server costs**, making a one-time purchase or a simple subscription model viable.

2. Core Features
================

- **Real-Time Finger Tracking:** Uses the phone's camera to track the user's hand position on the fretboard.
- **Instant Visual Feedback:** Overlays colored dots on the user's fingertips to indicate correct or incorrect placement.
- **Actionable Corrections:** Provides simple, text-based suggestions like "Finger 1 is too high."
- **Chord Correctness Score:** Displays a real-time accuracy score (0-100%) for the current chord.
- **Local Progress Tracking:** All practice sessions and scores are saved locally to the device's database.
- **Progress History:** Users can view charts and stats on their improvement over time for different chords.
- **Built-in Chord Library:** An offline library of common chords for users to practice.

3. Technical Architecture: The "Eyes" and the "Brain"
======================================================

The app's intelligence comes from a highly efficient, two-part system that runs entirely on the user's device.

3.1. The "Eyes" - Hand Landmark Detection (MediaPipe)
-----------------------------------------------------

- We will use Google's **MediaPipe Hand Landmarker**, a free, pre-trained computer vision model.
- Its only job is to analyze the camera feed at 30-60 FPS and output the precise 3D coordinates of the 21 joints on the user's hand.
- This is incredibly fast, efficient, and requires no network connection.

3.2. The "Brain" - The Rule Engine (Your Custom Logic)
------------------------------------------------------

- This is the core logic of the app that *you* will code. It is not a general AI, but a fast and precise set of rules.
- It takes the hand coordinates from MediaPipe and compares them against a pre-defined "template" for each chord.
- For a G-Major chord, it checks: "Is the user's index finger coordinate within the target zone for the 5th string, 2nd fret?"
- This engine is responsible for calculating the score, determining the feedback, and deciding which dots are red or green.

4. Technology Stack
===================

.. list-table::
   :widths: 25 25 50
   :header-rows: 1

   * - Category
     - Technology
     - Purpose
   * - Framework
     - React Native
     - Build a cross-platform app for iOS and Android.
   * - Camera Access
     - ``react-native-vision-camera``
     - Provides high-performance, direct access to the camera frame buffer.
   * - On-Device Vision
     - **MediaPipe Hand Landmarker**
     - The "eyes." Provides real-time 3D hand coordinates.
   * - Local Database
     - ``react-native-sqlite-storage``
     - The "memory." Stores all user progress and practice sessions locally.

5. Key Challenge: The Calibration Engine
========================================

The single greatest technical challenge is **Calibration**: teaching the app to understand where the guitar is in the physical world through the camera's 2D lens. This involves writing code to:

1. **Detect the Fretboard:** Identify the edges of the guitar neck.
2. **Detect Frets & Strings:** Find the grid of lines on the neck.
3. **Create a Coordinate System:** Map the pixel space of the camera to the musical space of ``(fret, string)``.

Solving this robustly is the key to making the entire rule engine work accurately.

6. Implementation Roadmap (Build Plan)
======================================

This project can be broken down into four distinct phases.

Phase 1: Foundation & Camera Setup (1-2 Weeks)
----------------------------------------------

- **Goal:** Get a live camera feed running and install all core dependencies.
- **Tasks:**
    1. Initialize a new React Native project.
    2. Install and configure ``react-native-vision-camera``.
    3. Create a basic screen that displays a full-screen, real-time camera view.
    4. Install ``react-native-sqlite-storage`` and ``@mediapipe/tasks-vision``.

Phase 2: The Vision Pipeline & Calibration (2-3 Weeks)
------------------------------------------------------

- **Goal:** Prove the core technical risk. Detect a hand and map it to a virtual fretboard.
- **Tasks:**
    1. Integrate MediaPipe Hand Landmarker to run on the camera's frame processor.
    2. Successfully get real-time 3D hand coordinates logging to the console.
    3. **(Hardest Task)** Develop the Calibration Engine: write the computer vision logic to detect the fretboard and create the coordinate transformation grid.
    4. Draw a simple overlay on the screen showing the detected fretboard grid.

Phase 3: The Rule Engine & UI Feedback (2 Weeks)
------------------------------------------------

- **Goal:** Translate the vision data into user-facing feedback.
- **Tasks:**
    1. Create the JSON data structure for defining chord rules (target finger, fret, string).
    2. Populate the JSON file with rules for 3-5 initial chords (e.g., G, C, D).
    3. Implement the core "Comparison Loop" logic that checks hand landmarks against the chord rules.
    4. Build the UI overlays: draw the colored dots on the user's fingers and display the text feedback and score.

Phase 4: Application Features & Polish (2-3 Weeks)
--------------------------------------------------

- **Goal:** Wrap the core feature in a complete, user-friendly application.
- **Tasks:**
    1. Design and implement the SQLite database schema for storing practice sessions and progress.
    2. Build the UI screens for the chord library, progress charts, and session history.
    3. Add a metronome and other practice tools.
    4. Implement the in-app purchase flow to unlock the full set of features/chords.
    5. Thoroughly test, debug, and polish the user experience.
