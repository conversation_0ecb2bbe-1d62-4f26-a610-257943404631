======================================================
Guitar Tutor: Video Analysis & Review - Build Plan
======================================================

.. contents::
   :local:

1. Project Overview
===================

This document outlines the build plan for a **premium guitar coaching app** that provides in-depth, AI-powered analysis of a user's playing technique. 

This architecture pivots from a real-time feedback model to an **asynchronous "Upload & Review" model**. The user records a video of their playing, uploads it, and receives a detailed, frame-by-frame analysis generated by a powerful cloud-based AI. This approach allows for far more nuanced and valuable feedback than a simple real-time checker.

The business model is built around a finite resource ("analysis credits"), making it cost-effective and sustainable.

2. Marketing & Two-App Strategy
===============================

This project is part of a larger, synergistic two-app ecosystem designed to maximize user reach and create a clear monetization path.

**App 1: "Real-Time Chord Trainer" (The Freemium Product)**

- **Purpose:** To be a self-sustaining, profitable app that also serves as a marketing funnel.
- **Functionality:** A fully offline, real-time feedback app using MediaPipe and a local rule engine.
- **Business Model:** Freemium.
    - **Free Tier:** Provides real-time feedback for a limited set of essential chords. This is the primary user acquisition driver.
    - **Premium Tier:** A one-time purchase or an affordable annual subscription unlocks all chords, advanced lessons, and additional features.
- **Upsell Mechanism:** The app will feature non-intrusive advertisements or CTAs promoting the premium analysis app.

**App 2: "AI Guitar Analyst" (The Premium Analysis Product - This Plan)**

- **Purpose:** The high-value, subscription-based product for serious learners.
- **Functionality:** The asynchronous "Upload & Review" app described in this document.
- **Business Model:** Monetized via a subscription or the sale of "analysis credits."

**The User Journey Funnel**

1.  A user discovers the highly-rated, free "Real-Time Chord Trainer" on the app store.
2.  They use and enjoy the free features. Some upgrade to the paid tier within this app to unlock more chords, generating a primary revenue stream.
3.  As they become more invested, they see messages promoting the next level of learning: "Great job on your G-Major! Want an expert analysis of your technique? Get feedback on your timing, finger arching, and more with our AI Guitar Analyst app."
4.  This CTA deep-links them to the "AI Guitar Analyst" app, creating a powerful, qualified lead for the premium service.

This strategy allows us to build a sustainable business where both apps are valuable on their own, but together they create a powerful growth engine.

3. User Flow: From Recording to Review
======================================

This section describes the step-by-step user journey through the app's core feature.

**Step 1: Start a Practice Session**

- **User Action:** The user opens the app and selects a chord they want to practice from their library (e.g., "G-Major").
- **App Response:** The app navigates to the recording screen. It displays the name of the selected chord and shows the user their remaining "Analysis Credits" (e.g., "3 Free Analyses Remaining").

**Step 2: Record a Video**

- **User Action:** The user presses the "Record" button.
- **App Response:** The app starts recording a video using the front-facing camera. A timer is displayed, perhaps with a 3-second countdown. The recording automatically stops after a set duration (e.g., 30 seconds).

**Step 3: "Smart Trim" Confirmation**

- **User Action:** The recording finishes.
- **App Response:** The app **does not** immediately upload. Instead, it performs a quick, on-device analysis using MediaPipe to find the most relevant segment of the video.
- **UI:** A new screen appears, showing a timeline of the video. The automatically selected segment (e.g., from timestamp 0:04 to 0:19) is highlighted.
- **Message:** "We've selected the most relevant part of your performance. Does this look right?"
- **User Action:**
    - The user taps the primary **"Confirm & Analyze"** button.
    - *Alternatively*, the user can tap "Adjust Selection" to manually drag the timeline handles before confirming.

**Step 4: Upload and Processing**

- **User Action:** The user confirms the selection.
- **App Response:**
    1. The app checks if the user has analysis credits available.
    2. If yes, it deducts one credit and starts uploading the trimmed video clip to the backend.
    3. A progress bar is shown for the upload.
    4. Once the upload is complete, a message appears: "Great! We've got your video. We'll notify you when your analysis is ready (usually in 1-2 minutes)." 
    5. The user is free to close the app or practice another chord.

**Step 5: Notification of Completion**

- **User Action:** The user waits.
- **App Response:** After the backend finishes the AI analysis, the app receives a **push notification**: "Your G-Major analysis is ready for review!"

**Step 6: The Review Experience**

- **User Action:** The user taps the notification or finds the completed analysis in their session history.
- **App Response:** The app opens the **Interactive Review Player** screen.
- **UI:**
    - The user's video plays in a loop.
    - **Synchronized Overlays:** As the video plays, visual feedback from the JSON analysis appears directly on the video (e.g., a red highlight on a misplaced finger at the exact moment it happens).
    - **Summary Panel:** A panel displays the overall score and a list of key feedback points, such as "Finger 2 is too low" or "Strumming was uneven at 0:08."
    - The user can pause, scrub, and re-watch their performance with the detailed, synchronized feedback.

**Step 7: Long-Term Progress**

- **User Action:** The user navigates to the "Progress" tab.
- **App Response:** The app displays charts showing the user's score for "G-Major" over time, allowing them to see their improvement clearly.

4. Anticipated Challenges & Solutions (Steelmanning)
==================================================

This section addresses potential weaknesses in the plan to ensure a more robust final product.

4.1. The "Perfect JSON" Fallacy
--------------------------------

- **Weakness:** The plan assumes the Gemini API will always return perfectly structured JSON. LLMs can be unpredictable and may return malformed data or conversational text.
- **Solution: AI Sanitization & Validation Layer:**
    1. **Backend Service:** Create a dedicated ``AiResponseParser`` service in Rails.
    2. **Robust Parsing:** This service will be responsible for parsing the AI response, validating its schema, and ensuring data types are correct.
    3. **Graceful Failure:** If parsing or validation fails, the background job will not crash. It will retry the API call (perhaps with a refined prompt) and log the failed response for manual review.

4.2. The Complexity of "Smart Trim"
-----------------------------------

- **Weakness:** Automatically identifying the "core performance" is non-trivial. Simple hand presence is not enough.
- **Solution: On-Device Motion Analysis:**
    1. **Prototype First:** Before full implementation, create a small test app to analyze landmark velocities from MediaPipe to establish reliable thresholds.
    2. **Stateful Logic:** Develop a ``PerformanceWindowFinder`` class on the client. This class will use a simple state machine (e.g., `IDLE`, `HOLDING`, `TRANSITIONING`) based on finger velocity to identify the most relevant, stable segment of the performance.

4.3. The User Experience of Waiting
-----------------------------------

- **Weakness:** A 1-2 minute wait for analysis can feel broken if the app provides no feedback.
- **Solution: Persistent "In-Progress" State:**
    1. **Immediate Feedback:** The API will immediately return a ``job_id`` and a ``status: 'queued'`` upon video upload.
    2. **Local State:** The client will save this state locally, instantly displaying the session in the user's history with a "Processing..." indicator.
    3. **Specific Notifications:** The final ActionCable notification will be tied to the specific ``job_id``, allowing the client to update the correct UI element to "Complete."

4.4. The Cost of Video Storage
------------------------------

- **Weakness:** Storing every user video indefinitely will become the largest and most unpredictable infrastructure cost.
- **Solution: Data Lifecycle & Retention Policy:**
    1. **Tiered Retention:** Implement a policy to automatically delete video files after a set period, while keeping the valuable (and tiny) JSON analysis data. For example, 14 days for free trial users, 1 year for subscribers.
    2. **Store Optimized Video:** Only store the trimmed and compressed video clip on the backend, never the original file.
    3. **User Control:** Provide a "Delete Video" option to allow users to manage their own data and reduce storage costs.

5. Core Features
================

- **Video Recording:** A simple in-app interface for recording short (10-60 second) practice sessions.
- **Smart Trimming (Frictionless Upload):** The app will **automatically detect the core performance** within the video and suggest a trimmed selection to the user, who can approve or adjust it. This saves cost and user effort.
- **Video Upload:** Seamlessly uploads the selected video clip to a secure backend for analysis.
- **Push Notifications:** Notifies the user when their analysis is complete and ready for review.
- **Interactive Review Player:** A specialized video player that displays the user's original video alongside synchronized, AI-generated feedback and visual overlays.
- **Historical Progress Tracking:** All analyses are saved, allowing the user to track their improvement over time with charts and statistics, creating a valuable long-term record of their journey.

6. Technical Architecture: Asynchronous & Server-Centric
========================================================

This architecture separates the application into a lightweight client and a powerful backend responsible for the heavy lifting.

6.1. React Native Client (The "Capture & Display" App)
------------------------------------------------------

The client's primary jobs are to record video, manage uploads, and present the final analysis in a compelling way.

- **Video Capture:** Uses ``react-native-vision-camera`` to record video.
- **On-Device Pre-Processing:** Before uploading, the app uses **MediaPipe** locally to perform "Smart Trimming" and pre-validation checks (e.g., ensuring a hand is visible).
- **API Communication:** Uses ``axios`` to upload video files to the backend and manage user data.
- **Real-Time Notifications:** Uses a WebSocket client to listen for the "analysis complete" signal from the server.
- **Review UI:** A custom-built screen that plays the user's video while rendering overlays based on the JSON feedback received from the server.

6.2. Rails Backend (The "Analysis & Data" Hub)
------------------------------------------------

The backend manages storage, user data, and the core AI analysis pipeline.

- **API:** A simple REST API to handle user authentication, video uploads, and data synchronization.
- **File Storage:** Uses ActiveStorage to manage video uploads to a cloud provider like Amazon S3 or Google Cloud Storage.
- **Background Job Processing:** Uses a queueing system (e.g., Sidekiq, GoodJob) to process video analyses asynchronously. This is crucial for handling tasks that may take several minutes without blocking the server.
- **AI Integration:** The background job calls the **Gemini 1.5 Pro** API, sending the video file and receiving a structured JSON object with the detailed analysis.
- **Real-Time Notifications:** Uses **ActionCable** (WebSockets) to send a simple, real-time message back to the specific user's client when their analysis job is complete.

7. Technology Stack
===================

.. list-table::
   :widths: 20 30 50
   :header-rows: 1

   * - Layer
     - Technology
     - Purpose
   * - **Client**
     - React Native
     - Core cross-platform framework.
   * - 
     - ``react-native-vision-camera``
     - High-performance video recording.
   * - 
     - MediaPipe
     - **On-device pre-processing** for Smart Trimming & validation.
   * - 
     - ``axios``
     - HTTP client for uploading video and syncing data.
   * - 
     - ``@react-native-community/websocket``
     - Listens for real-time push notifications from the server.
   * - **Backend**
     - Ruby on Rails 7+
     - Robust framework for the API and backend logic.
   * - 
     - PostgreSQL
     - Primary database for user data, video metadata, and analysis results.
   * - 
     - ActiveStorage (with S3/GCS)
     - Manages direct-to-cloud video uploads.
   * - 
     - Sidekiq or GoodJob
     - Background job processing for the AI analysis queue.
   * - 
     - ActionCable
     - Real-time WebSocket communication for notifications.
   * - **AI Service**
     - **Gemini 1.5 Pro**
     - The core AI model for performing the video analysis.

8. Implementation Roadmap
=========================

This plan is designed to build the system from the backend out, ensuring the core pipeline works before building the full UI.

Phase 1: Backend Foundation (2 Weeks)
-------------------------------------

- **Goal:** Set up the complete server infrastructure.
- **Tasks:**
    1. Initialize Rails API app with PostgreSQL.
    2. Configure ActiveStorage for cloud uploads (e.g., S3).
    3. Set up a background job processor (e.g., Sidekiq).
    4. Create User model and authentication endpoints.
    5. Create PracticeVideo model for storing video metadata and analysis results.
    6. Implement the ``/uploads`` endpoint that takes a video file and creates a ``PracticeVideo`` record and an analysis job.

Phase 2: AI Analysis Pipeline (2 Weeks)
---------------------------------------

- **Goal:** Get a successful end-to-end analysis loop working.
- **Tasks:**
    1. Write the background job (e.g., ``GuitarAnalysisJob``).
    2. Integrate the Gemini 1.5 Pro client.
    3. Develop a detailed prompt to send to the Gemini API, requesting structured JSON output.
    4. Write the logic to parse the AI's JSON response and save it to the ``PracticeVideo`` record.
    5. Configure ActionCable to broadcast a "complete" message to the user upon job completion.

Phase 3: Client Capture & Upload (2 Weeks)
------------------------------------------

- **Goal:** Build the client-side functionality for recording and uploading a video.
- **Tasks:**
    1. Build the video recording screen using ``react-native-vision-camera``.
    2. Implement the **Smart Trimming** feature using a local MediaPipe pass to find the core action window.
    3. Create the UI for the user to confirm or adjust the trimmed selection.
    4. Write the service to upload the final video clip to the Rails backend.
    5. Implement the WebSocket listener to receive the "complete" notification.

Phase 4: The Review Experience (2-3 Weeks)
------------------------------------------

- **Goal:** Build the high-value playback and analysis UI.
- **Tasks:**
    1. Create the "Analysis Review" screen.
    2. Build the custom video player.
    3. Write the logic to parse the JSON feedback data.
    4. Implement the rendering of synchronized overlays, text, and scores on top of the video as it plays.
    5. Build the UI for viewing historical progress and charts.

Phase 5: Monetization & Polish (1-2 Weeks)
------------------------------------------

- **Goal:** Implement the business logic and polish the app for release.
- **Tasks:**
    1. Implement the "Analysis Credit" system on the backend.
    2. Integrate RevenueCat or a similar service for managing in-app purchases and subscriptions.
    3. Build the UI for the paywall and credit pack purchasing.
    4. Final testing, bug fixing, and UX improvements.
