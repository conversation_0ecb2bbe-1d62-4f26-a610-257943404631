==========================================================
MLP Build Plan: AI Guitar Analyst (Video Review App)
==========================================================

.. contents::
   :local:

1. MLP Philosophy: The One-Button Analyzer
===========================================

The goal of this Minimum Lovable Product (MLP) is to validate the core business hypothesis: **"Will users pay for a high-quality, AI-powered analysis of their guitar playing?"**

To achieve this, we will strip away all non-essential features and build a single, elegant user flow: Record -> Upload -> Review. The focus is on delivering the core value proposition with minimal infrastructure and development time.

This version will be anonymous and session-based, postponing features like user accounts, history, and complex UI overlays.

2. Simplified MLP User Flow
===========================

1.  **Record:** The app opens to a single screen with a "Record" button. The user records a fixed-length 15-second video.
2.  **Upload & Wait:** The app immediately uploads the video to a single, synchronous API endpoint. The UI will show a loading animation and engaging text (e.g., "Our AI is analyzing your technique...") while it waits for the response (approx. 30-60 seconds).
3.  **Review:** Once the analysis is complete, the app navigates to a simple review screen. It displays the user's video and the AI-generated feedback as formatted text below. There are no synchronized overlays in this version.
4.  **Monetize:** After one free analysis, the "Analyze" button becomes disabled. Tapping it prompts the user with a one-time in-app purchase to unlock unlimited analyses.

3. Simplified Technical Architecture
==================================

**Backend: The Serverless "Black Box"**

- **Framework:** A single **Serverless Function** (e.g., AWS Lambda, Google Cloud Function) to minimize cost and infrastructure management.
- **API Endpoint:** One synchronous endpoint: ``POST /analyze``.
    - It accepts a video file.
    - It calls the Gemini 1.5 Pro API directly.
    - It waits for the Gemini response and returns the resulting JSON directly to the client.
- **No Database or User Accounts:** This MLP version is stateless and does not require a database, user management, or permanent video storage.

**Client: The "One-Button" App**

- **Framework:** React Native.
- **Core Libraries:** ``react-native-vision-camera`` for recording, ``axios`` for uploading.
- **No Local Database:** No SQLite or complex data persistence is needed for the MLP.
- **No Smart Trimming:** The user is instructed to play immediately to simplify the recording process.

4. MLP Implementation Plan (4-6 Weeks)
======================================

**Phase 1: The Backend Black Box (1-2 Weeks)**

- **Goal:** Create the serverless endpoint.
- **Tasks:**
    1. Set up a serverless function project.
    2. Write the ``/analyze`` function that accepts a video file.
    3. Integrate the Gemini 1.5 Pro SDK.
    4. Implement the direct API call and return the response.
    5. Deploy the function.

**Phase 2: The Client App (2-3 Weeks)**

- **Goal:** Build the minimal UI to interact with the backend.
- **Tasks:**
    1. Initialize a React Native project.
    2. Build the single recording screen.
    3. Implement the video upload logic to the ``/analyze`` endpoint.
    4. Create the loading/waiting screen.
    5. Build the simple, non-interactive review screen to display the video and the formatted text feedback.

**Phase 3: Monetization (1 Week)**

- **Goal:** Test the payment hypothesis.
- **Tasks:**
    1. Use a service like RevenueCat for a simple in-app purchase implementation.
    2. Implement the logic to allow one free analysis.
    3. Build the simple paywall screen that appears on the second attempt.
